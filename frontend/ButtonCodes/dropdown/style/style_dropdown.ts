import React from 'react';

// 下拉按键样式接口
export interface DropdownButtonStyles {
  dropdown: React.CSSProperties;
  dropdownMenu: React.CSSProperties;
  text: React.CSSProperties;
  icon: React.CSSProperties;
}

// 菜单按键样式接口
export interface MenuItemStyles {
  button: React.CSSProperties;
  add: React.CSSProperties;
  addIcon: React.CSSProperties;
  storage: React.CSSProperties;
  deleteIcon: React.CSSProperties;
}

// 对话框样式接口
export interface DialogStyles {
  overlay: React.CSSProperties;
  dialog: React.CSSProperties;
  closeIcon: React.CSSProperties;
  label: React.CSSProperties;
  input: React.CSSProperties;
  errorMessage: React.CSSProperties;
  confirmButton: React.CSSProperties;
}

// 下拉按键默认样式
export const dropdownButtonStyles: DropdownButtonStyles = {
  // 按键样式
  dropdown: {
    width: '200px',
    height: '50px',
    borderRadius: '0px',
    backgroundColor: '#f1f1f1',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    cursor: 'pointer',
    position: 'relative',
    border: 'none',
    outline: 'none',
  },
  
  // 下拉菜单样式
  dropdownMenu: {
    position: 'absolute',
    top: '100%',
    left: '0',
    width: '200px',
    maxHeight: '300px',
    overflowY: 'auto',
    backgroundColor: '#ffffff',
    border: '1px solid #cccccc',
    display: 'none',
    zIndex: 1000,
  },
  
  // 文字样式
  text: {
    fontSize: '20px',
    color: '#242424',
    textAlign: 'center',
  },
  
  // 图标样式
  icon: {
    position: 'absolute',
    right: '10px',
    fontSize: '20px',
    color: '#cccccc',
  },
};

// 菜单按键样式
export const menuItemStyles: MenuItemStyles = {
  // 默认按键样式
  button: {
    width: '100%',
    padding: '10px',
    border: 'none',
    backgroundColor: 'transparent',
    cursor: 'pointer',
    fontSize: '15px',
    color: '#2d2d2d',
    textAlign: 'center',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
  
  // 新建按键样式
  add: {
    width: '100%',
    padding: '10px',
    border: 'none',
    backgroundColor: 'transparent',
    cursor: 'pointer',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
  
  // 新建按键图标样式
  addIcon: {
    fontSize: '15px',
    color: '#cccccc',
  },
  
  // 存储按键样式
  storage: {
    width: '100%',
    padding: '10px',
    border: 'none',
    backgroundColor: 'transparent',
    cursor: 'pointer',
    fontSize: '15px',
    color: '#2d2d2d',
    textAlign: 'center',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  },
  
  // 删除图标样式
  deleteIcon: {
    position: 'absolute',
    right: '20px',
    fontSize: '15px',
    color: '#0d0d0d',
    cursor: 'pointer',
  },
};

// 对话框样式
export const dialogStyles: DialogStyles = {
  // 遮罩层样式
  overlay: {
    position: 'fixed',
    top: '0',
    left: '0',
    width: '100vw',
    height: '100vh',
    backgroundColor: '#000000',
    opacity: '0.7',
    zIndex: 9999,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
  
  // 对话框样式
  dialog: {
    backgroundColor: '#ffffff',
    opacity: '1',
    height: '250px',
    width: '400px',
    borderRadius: '10px',
    zIndex: 10000,
    position: 'relative',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
  },
  
  // 关闭图标样式
  closeIcon: {
    position: 'absolute',
    top: '20px',
    right: '20px',
    fontSize: '30px',
    color: '#0d0d0d',
    cursor: 'pointer',
  },
  
  // 标签样式
  label: {
    fontSize: '25px',
    color: '#9f9f9f',
    marginBottom: '5px',
    alignSelf: 'flex-start',
    marginLeft: '25px',
  },
  
  // 输入框样式
  input: {
    height: '50px',
    width: '350px',
    backgroundColor: '#ffffff',
    border: '2px solid #9f9f9f',
    borderRadius: '5px',
    color: '#9f9f9f',
    fontSize: '20px',
    paddingLeft: '5px',
    cursor: 'text',
    outline: 'none',
  },
  
  // 错误信息样式
  errorMessage: {
    fontSize: '20px',
    color: '#ff0000',
    opacity: '0.5',
    textAlign: 'center',
    marginTop: '10px',
    display: 'none',
  },
  
  // 确认按键样式
  confirmButton: {
    position: 'absolute',
    bottom: '25px',
    right: '25px',
    height: '30px',
    width: '80px',
    backgroundColor: '#0d0d0d',
    color: '#ffffff',
    border: 'none',
    borderRadius: '5px',
    cursor: 'pointer',
    fontSize: '16px',
  },
};

// 显示下拉菜单的样式
export const showDropdownMenu: React.CSSProperties = {
  display: 'block',
};

// 隐藏下拉菜单的样式
export const hideDropdownMenu: React.CSSProperties = {
  display: 'none',
};

// 显示错误信息的样式
export const showErrorMessage: React.CSSProperties = {
  display: 'block',
};

// 隐藏错误信息的样式
export const hideErrorMessage: React.CSSProperties = {
  display: 'none',
};
