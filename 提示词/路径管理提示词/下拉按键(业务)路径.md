# 下拉按键(业务)文件路径

## 创建下拉文件

**文件夹:**

- 1.读取下拉按键文件夹‘dropdown’ -> (根目录/frontend/ButtonCodes)
  - 1.创建下拉按键事件文件夹‘event’
  - 2.创建下拉按键组件文件夹‘DropdownButton’

**代码文件:**

- 1.创建下拉按键事件文件‘event_dropdown.ts’ -> (根目录/frontend/ButtonCodes/dropdown/event)
- 2.创建下拉按键组件文件‘DropdownButton.tsx’ -> (根目录/frontend/ButtonCodes/dropdown/DropdownButton)
- 3.创建按键统一导出入口文件‘index.ts’ -> (根目录/frontend/ButtonCodes)

## 演示文件路径

**1.演示文件夹:**

- 1.创建下拉按键演示文件夹‘dropdown-test’ -> (根目录/apps/frontend/ButtonCodes)
  - 1.创建下拉按键演示文件‘DropdownButton_test.tsx’
  
**2.创建按键演示文件(secondary-demo/page.tsx):**

- 1.创建下拉按键演示页面文件夹‘dropdown-demo’ -> (根目录/frontend/app)
  - 1.创建普通按键演示页面文件‘page.tsx’
