# 下拉按键(样式)

## 架构属性

**1.标准类型:**

- 1.React标准类型

## 下拉按键样式

**1.默认样式(style_dropdown.ts):**

- 1.按键样式:
  - 1.按键名称:dropdown
  - 2.按键形状:长方形
  - 3.按键高度:50px
  - 4.按键宽度:200px
  - 5.按键圆角:0px
  - 6.按键底色:#f1f1f1
  - 7.展示方式:弹性布局
  - 8.主轴对齐:水平居中
  - 9.交叉轴对齐:垂直居中
  - 10.鼠标指针样式:手型
- 2.下拉菜单样式:
  - 1.菜单形状:长方形
  - 2.菜单宽度:等于按键宽
  - 3.菜单最大高度:300px
  - 4.菜单滚动条:出现时显示
  - 5.菜单定位:绝对定位
  - 6.菜单隐藏:默认不显示
- 3.文字样式:
  - 1.默认文本:按键
  - 2.字体大小:20px
  - 3.文本颜色:#242424
  - 4.文本对齐:居中
-4.图标显示:
  - 1.图标类型:下拉箭头
  - 2.图标位置:
    - 1.右对齐:10px
  - 3.图标大小:20px
  - 4.图标颜色: #cccccc

## 菜单按键样式

**1.默认模式(style_dropdown.ts):**

- 1.默认按键(mode:button)
  - 1.键内文本
    - 1.文本颜色: #2d2d2d
    - 2.文本大小:15px
    - 3.对齐方式:水平居中

**2.新建模式(style_dropdown.ts):**

- 1.新建按键(mode:add)
  - 1.样式:
    - 1.图标类型:加号'+'图案，无底色
    - 2.图标大小:15px
    - 3.图标颜色: #cccccc
    - 4.对齐方式:水平居中

**3.存储按键(style_dropdown.ts):**

- 1.存储按键
  - 1.键内文本
    - 1.文本颜色: #2d2d2d
    - 2.文本大小:15px
    - 3.对齐方式:水平居中
  - 2.‘删除’图标
    - 1.图标
      - 1.图标类型:减号'-'图案，无底色
      - 2.图标颜色: #0d0d0d
      - 3.图标位置:
        - 1.右对齐:20px
      - 4.图标大小:15px

## 依赖组件样式

**1.对话框弹窗(style_dropdown.ts):**

- 1.说明:
  - 1.该组件由‘新建’模式(add)按键触发，其样式独立于下拉菜单

- 2.对话框样式:
  - 1.遮罩层样式:
    - 1.定位方式:固定定位
    - 2.尺寸:铺满整个视窗
    - 3.背景颜色: #000000
    - 4.透明度:0.7
    - 5.层级:页面顶层
    - 6.展示方式:弹性布局
    - 7.主轴对齐:垂直,水平对齐
  - 2.对话框样式:
    - 1.背景颜色: #ffffff
    - 2.透明度:完全不透明,不受遮罩层影响
    - 3.高度:250px
    - 4.宽度:400px
    - 5.圆角:10px
    - 6.层级:遮罩层之上
  - 3.框内布局:
    - 1.对齐方式:垂直，水平居中
    - 2.关闭图标
      - 1.图标样式:'x'图案，无底色
      - 2.图案颜色: #0d0d0d
      - 3.图案位置:
        - 1.右对齐:20px
      - 4.图案大小:30px
    - 3.输入框
      - 1.标签
        - 1.标签文本:名称
        - 2.字体大小:25px
        - 3.字体颜色: #9f9f9f
        - 4.与输入框间隔:5px
        - 5.对齐方式:
          - 1.左对齐:25px
      - 2.输入框
        - 1.输入框高:50px
        - 2.输入框宽:350px
        - 3.背景颜色: #ffffff
        - 4.输入边框:2px
        - 5.边框颜色: #9f9f9f
        - 6.边框圆角:5px
        - 7.文本颜色: #9f9f9f
        - 8.文本大小:20px
        - 9.对齐方式:左对齐:5px
        - 10.鼠标指针样式:文本
      - 3.错误信息
        - 1.显示位置:
          - 1.顶对齐:10px
        - 2.字体大小:20px
        - 3.字体颜色: #ff0000
        - 4.透明度:50%
        - 5.默认隐藏
        - 6.字体对齐:居中
        - 7.触发时长:显示3s
    - 4.确认按键:
      - 1.按键文本:确认
      - 2.文本颜色: #ffffff
      - 3.按键高度:30px
      - 4.按键宽度:80px
      - 5.按键底色: #0d0d0d
      - 6.按键位置:
        - 1.底部对齐:25px
        - 2.右部对齐:25px
      - 7.按键圆角:5px
