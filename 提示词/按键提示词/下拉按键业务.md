# 下拉按键(业务)

## 架构属性

**1.标准类型:**

- 1.React标准类型

## 下拉按键交互

**1.鼠标悬停/点击(event_dropdown.ts):**

- 1.菜单选项:
  - 1.鼠标悬停:
    - 1.默认模式(button)
      - 1.激活
        - 1.底色: #929292
        - 2.悬停: #b6b6b6
      - 2.未激活
        - 1.底色: #f1f1f1
        - 2.悬停: #dbdbdb
    - 2.新建模式(add)
      - 1.悬停底色: #dbdbdb
      - 2.鼠标按下: #929292
      - 3.鼠标松开: #f1f1f1
    - 3.对话框
      - 1.‘关闭’图标
        - 1.悬停颜色: #5e5e5e
      - 2.‘确认’按键
        - 1.悬停颜色: #5e5e5e
    - 4.存储按键
      - 1.激活
        - 1.底色: #929292
        - 2.悬停: #b6b6b6
      - 2.未激活
        - 1.底色: #f1f1f1
        - 2.悬停: #dbdbdb
      - 3.‘删除’图标(仅触发图标本身)
        - 1.悬停颜色: #5e5e5e
        - 2.悬停放大:2

**2.按键交互(event_dropdown.ts):**

- 1.点击下拉按键本身，展开/隐藏下拉菜单
- 2.点击菜单以外的任意地方，隐藏下拉菜单
- 4.对话框打开时，点击对话框区域不会隐藏下拉菜单
- 4.菜单选择规则
  - 1.选择模式为互斥单选,当一个选项被激活时，其他选项变为未激活状态
- 5.交互方式:
  - 1.'默认'模式(button)
    - 1.点击后，该选项变为“激活”状态
  - 2.'新建'模式(add)
    - 1.点击显示对话框
      - 1.输入名称验证
        - 1.报错: 不能为空、不能与现有名称重复
      - 2.点击‘确认’
        - 1.创建新'存储'按键菜单，并采用该名称对按键命名
        - 2.新菜单项位置: 添加到所有可选择项的末尾，但在‘新建’模式按键之上
        - 3.创建成功后，新创建的菜单项自动变为“激活”状态
      - 3.点击‘关闭’
        - 1.点击‘关闭’图标,关闭对话框
      - 4.'存储'按键
        - 1.点击选项主体，该选项变为“激活”状态
        - 2.点击'删除'图标
          - 1.从列表中移除该菜单项
          - 2.按键状态
            - 1.激活状态
              - 1.自动激活菜单中的首个按键
            - 2.非激活状态
              - 1.保持当前按键已激活状态
- 6.默认按键:
  - 1.默认存在一个‘默认’模式(button)按键
  - 2.名称: '默认'
- 7.规则:
  - 1.‘默认’模式按键是基础按键，不提供删除功能
  - 2.‘新建’模式(add)按键的显示前提是：菜单栏中至少存在一个‘默认’模式(button)按键
  - 3.'新建'模式(add)按键永远位于下拉列表的最下方

## 按键组件

**1.创建普通按键组件(DropdownButton.tsx):**

- 1.组件应接收‘mode’属性来控制其行为和样式
- 2.组件内部需处理点击事件，并根据不同模式调用外部函数

**2.导出按键组件(index.ts):**

- 1.导出`DropdownButton`组件及相关的类型定义
