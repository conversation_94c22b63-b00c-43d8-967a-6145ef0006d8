# 普通按键

## 初始提示词

- 1.阅读‘下拉按键路径.md’并用于创建文件
- 2.阅读‘前端技术栈.md’并用于构建代码
- 3.严格执行‘下拉按键样式.md’中的指示
- 4.禁止提前阅读除提示词描述以外的文档
- 5.仅完成该文本提供的逻辑流程，禁止补全
- 6.更新.gitignore文档

## 优化提示词

请按照以下步骤顺序执行下拉按键组件的开发任务：

1. **文件路径规划**：首先阅读 `下拉按键(样式)路径.md` 文件，了解需要创建的文件结构和路径规范，然后根据该文档创建相应的文件和文件夹
2. **技术栈确认**：阅读 `前端技术栈.md` 文件，了解项目使用的前端技术栈（如React、Vue、Angular等），并基于这些技术栈来编写代码
3. **样式实现**：严格按照 `下拉按键样式.md` 文件中的具体样式要求和设计规范来实现下拉按键的外观和交互效果
4. **文档读取限制**：在执行过程中，除了上述三个指定的提示词文档外，不要主动阅读项目中的其他文档文件
5. **功能范围限制**：严格按照本指令提供的逻辑流程执行，不要添加额外的功能或优化，只完成下拉按键组件的基本实现
6. **版本控制更新**：完成代码实现后，检查并更新项目根目录下的 `.gitignore` 文件，确保新增的文件符合版本控制规范

注意：请严格按照步骤顺序执行，每个步骤完成后再进行下一步
