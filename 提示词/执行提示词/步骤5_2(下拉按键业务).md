# 普通按键

## 初始提示词

- 1.阅读‘下拉按键(业务)路径.md’并用于创建文件
- 2.阅读‘前端技术栈.md’并用于构建代码
- 3.严格执行‘下拉按键(业务).md’中的指示
- 4.阅读‘下拉按键演示.md’并用于创建演示文件
- 5.禁止提前阅读除提示词描述以外的文档
- 6.仅完成该文本提供的逻辑流程，禁止补全
- 7.更新.gitignore文档

## 优化提示词

请按照以下步骤顺序执行下拉按键组件的开发任务：

1. **阅读规划文档**：首先阅读 `下拉按键(业务)路径.md` 文件，了解文件结构和路径规划，用于指导后续文件创建的位置和组织结构
2. **了解技术栈**：阅读 `前端技术栈.md` 文件，掌握项目使用的前端技术栈（如React、TypeScript、样式框架等），确保代码实现符合项目技术规范
3. **执行业务逻辑**：严格按照 `下拉按键(业务).md` 文件中的详细指示和业务需求，实现下拉按键的核心功能逻辑，不得偏离或修改其中的业务规则
4. **创建演示文件**：根据 `下拉按键演示.md` 文件的说明，创建相应的演示页面或组件，用于展示下拉按键功能的使用效果
5. **严格遵循文档顺序**：在执行过程中，严禁提前阅读或参考除上述指定文档以外的任何其他文档，确保按照既定流程进行
6. **限制开发范围**：仅实现当前任务中明确要求的功能逻辑，不得自行添加额外功能或进行功能扩展
7. **更新项目配置**：完成功能开发后，检查并更新 `.gitignore` 文件，确保新增的文件和目录按照项目规范进行版本控制管理

注意：请严格按照步骤顺序执行，每个步骤完成后再进行下一步
